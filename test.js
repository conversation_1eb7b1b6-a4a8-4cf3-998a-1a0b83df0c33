
jQuery(document).ready(function($) {
  // Wait for Divi slider to be fully initialized

  $("img").each(function(index){
    $(this).removeAttr("title");
  });

  setTimeout(function() {
    // Check if the slider exists
    if ($('.character-slider').length === 0) {
      console.log('Character slider not found on page');
      return;
    }
    
    // Count the actual number of slides
    var slideCount = $('.character-slider .et_pb_slide').length;
    console.log('Found ' + slideCount + ' slides in character slider');
    
    // Limit buttons to the actual number of slides (max 6)
    slideCount = Math.min(slideCount, 6);
    
    // Create the custom navigation buttons
    var buttonContainer = $('<div class="character-slider-nav"></div>');
    
    // Add buttons with images to the container (only for existing slides)
    for (var i = 0; i < slideCount; i++) {
      // Create button with empty data attributes initially
      var button = $('<button class="character-slider-btn" data-slide="' + i + '"></button>');
      // Create image with empty src initially
      var image = $('<img src="" alt="Character ' + (i+1) + '">');
      button.append(image);
      buttonContainer.append(button);
    }
    
    // Only proceed if we have slides
    if (slideCount === 0) {
      console.log('No slides found in character slider');
      return;
    }
    
    // Add the button container after the character slider
    $('.character-slider').after(buttonContainer);
    
    // Style the buttons and container
    $('.character-slider-nav').css({
      'display': 'flex',
      'justify-content': 'center',
      'margin-top': '20px',
      'gap': '15px'
    });
    
    $('.character-slider-btn').css({
      'background': 'transparent',
      'border': 'none',
      'cursor': 'pointer',
      'padding': '5px',
      'transition': 'all 0.3s ease'
    });
    
    $('.character-slider-btn img').css({
      'width': '180x',
      'height': '180px',
      'object-fit': 'cover',
      'transition': 'all 0.3s ease'
    });
    
    // Check if slider has Divi's slider functionality
    var sliderObj = $('.character-slider').data('et_pb_simple_slider');
    if (!sliderObj) {
      console.log('Slider object not found. Using alternative method.');
    }
    
    // Function to set a button to active state
    function setButtonActive(index) {
      // Reset all buttons to inactive state
      $('.character-slider-btn').css({
        'transform': 'scale(1)'
      });
      
      $('.character-slider-btn').each(function() {
        var inactiveImg = $(this).data('inactive-img');
        $(this).find('img').attr('src', inactiveImg);
      });
      
      // Set the target button to active state
      var activeButton = $('.character-slider-btn[data-slide="' + index + '"]');
      activeButton.css({
        'transform': 'scale(1.1)'
      });
      
      var activeImg = activeButton.data('active-img');
      activeButton.find('img').attr('src', activeImg);
    }
    
    // Function to get the current slide index
    function getCurrentSlideIndex() {
      return $('.character-slider .et-pb-active-slide').index();
    }
    
    // Set the first button to active state initially
    setButtonActive(0);
    
    // Handle button clicks
    $('.character-slider-btn').on('click', function() {
      var slideIndex = $(this).data('slide');
      
      // Go to the selected slide
      if (sliderObj && typeof sliderObj.et_slider_move_to === 'function') {
        sliderObj.et_slider_move_to(slideIndex);
      } else {
        // Alternative method if Divi's slider API is not available
        $('.character-slider .et-pb-controllers a').eq(slideIndex).trigger('click');
      }
      
      // Update button states
      setButtonActive(slideIndex);
    });
    
    // Update button states when slider changes
    $('.character-slider').on('slide.et_pb_simple_slider', function(event, slideIndex) {
      setButtonActive(slideIndex);
    });
    
    // Alternative event listener for slide changes via dot controllers
    $('.character-slider .et-pb-controllers a').on('click', function() {
      var slideIndex = $(this).index();
      
      // Manually trigger our button update logic after a short delay
      setTimeout(function() {
        setButtonActive(slideIndex);
      }, 100);
    });
    
    // Handle arrow navigation
    $('.character-slider .et-pb-arrow-prev, .character-slider .et-pb-arrow-next').on('click', function() {
      // Wait a moment for the slide transition to complete
      setTimeout(function() {
        var currentIndex = getCurrentSlideIndex();
        setButtonActive(currentIndex);
      }, 300);
    });
    
    // Remove this entire interval if other event handlers are sufficient
    // setInterval(function() {
    //   var currentIndex = getCurrentSlideIndex();
    //   setButtonActive(currentIndex);
    // }, 1000);
    
    $('.character-slider-btn[data-slide="0"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char1-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char1.png');
    $('.character-slider-btn[data-slide="1"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char2-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char2.png');
    $('.character-slider-btn[data-slide="2"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char3-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char3.png');
    $('.character-slider-btn[data-slide="3"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char4-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char4.png');
    $('.character-slider-btn[data-slide="4"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char5-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char5.png');
    $('.character-slider-btn[data-slide="5"]').data('inactive-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char6-active.png').data('active-img', 'https://www.blitsgames.com/wp-content/uploads/2025/06/char6.png');

    // Set initial image sources after setting data attributes
    $('.character-slider-btn').each(function() {
      var inactiveImg = $(this).data('inactive-img');
      $(this).find('img').attr('src', inactiveImg);
    });
    
    // Remove toggle functionality from first slide
    // $('.character-slider .et_pb_slide:first-child img').css('cursor', 'pointer');
    // $('.character-slider .et_pb_slide:first-child img').on('click', function() { ... });

    // Add toggle functionality for the second and third slide images
    $('.character-slider .et_pb_slide:nth-child(2) img, .character-slider .et_pb_slide:nth-child(3) img').css('cursor', 'pointer');

    // Define custom toggle image URLs with responsive variants
    var customToggleImage1 = {
      default: 'http://www.blitsgames.com/wp-content/uploads/2025/06/haelo-1.png',
      mobile: 'http://www.blitsgames.com/wp-content/uploads/2025/06/haelo-mobile1.png'
    };

    var customToggleImage2 = {
      default: 'http://www.blitsgames.com/wp-content/uploads/2025/06/horne.png',
      mobile: 'http://www.blitsgames.com/wp-content/uploads/2025/06/horne-mobile1.png'
    };

    // Function to get the appropriate image based on viewport width
    function getResponsiveImage(imageObj) {
      return (window.innerWidth < 800) ? imageObj.mobile : imageObj.default;
    }

    // Toggle functionality for second slide image
    $('.character-slider .et_pb_slide:nth-child(2) img').on('click', function() {
      var $this = $(this);
      var currentSrc = $this.attr('src');
      var currentSrcset = $this.attr('srcset') || '';
      var altSrc = $this.data('alt-src');
      var altSrcset = $this.data('alt-srcset');
      
      console.log('Second slide image clicked. Current src:', currentSrc);
      
      // If this is the first click, store the original src/srcset
      if (!altSrc) {
        $this.data('alt-src', currentSrc);
        $this.data('alt-srcset', currentSrcset);
        altSrc = getResponsiveImage(customToggleImage1);
        altSrcset = ''; // Clear srcset to ensure src is used
        console.log('First click, setting alt src to:', altSrc);
      }
      
      // Toggle between the two images
      $this.attr('src', altSrc);
      $this.attr('srcset', altSrcset || '');
      $this.data('alt-src', currentSrc);
      $this.data('alt-srcset', currentSrcset);
      console.log('After toggle - New src:', $this.attr('src'), 'New srcset:', $this.attr('srcset'));
    });

    // Toggle functionality for third slide image
    $('.character-slider .et_pb_slide:nth-child(3) img').on('click', function() {
      var $this = $(this);
      var currentSrc = $this.attr('src');
      var currentSrcset = $this.attr('srcset') || '';
      var altSrc = $this.data('alt-src');
      var altSrcset = $this.data('alt-srcset');
      
      console.log('Third slide image clicked. Current src:', currentSrc);
      
      // If this is the first click, store the original src/srcset
      if (!altSrc) {
        $this.data('alt-src', currentSrc);
        $this.data('alt-srcset', currentSrcset);
        altSrc = getResponsiveImage(customToggleImage2);
        altSrcset = ''; // Clear srcset to ensure src is used
        console.log('First click, setting alt src to:', altSrc);
      }
      
      // Toggle between the two images
      $this.attr('src', altSrc);
      $this.attr('srcset', altSrcset || '');
      $this.data('alt-src', currentSrc);
      $this.data('alt-srcset', currentSrcset);
      console.log('After toggle - New src:', $this.attr('src'), 'New srcset:', $this.attr('srcset'));
    });

    // Add resize event listener to update images if viewport size changes while toggled
    $(window).on('resize', function() {
      // Check if second slide image is toggled
      var $secondImg = $('.character-slider .et_pb_slide:nth-child(2) img');
      if ($secondImg.data('alt-src') && $secondImg.attr('src') !== $secondImg.data('alt-src')) {
        $secondImg.attr('src', getResponsiveImage(customToggleImage1));
      }
      
      // Check if third slide image is toggled
      var $thirdImg = $('.character-slider .et_pb_slide:nth-child(3) img');
      if ($thirdImg.data('alt-src') && $thirdImg.attr('src') !== $thirdImg.data('alt-src')) {
        $thirdImg.attr('src', getResponsiveImage(customToggleImage2));
      }
    });
   
    // Then refresh the current active button
    var currentSlide = $('.character-slider .et-pb-active-slide').index();
    setButtonActive(currentSlide);
  
  }, 1500); // Wait 1.5 seconds for Divi to initialize
});

