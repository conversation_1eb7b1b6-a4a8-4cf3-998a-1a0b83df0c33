
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

<!-- jQ<PERSON><PERSON> Scroll Snap Plugin -->
<script>/**
 * Auto-scroll functionality with configurable options
 * @param {Object} options - Configuration options
 * @param {string} options.targetSelector - Selector for the target element to scroll to
 * @param {number} options.scrollSpeed - Duration of scroll animation in milliseconds (default: 800)
 * @param {number} options.scrollThreshold - Minimum scroll amount to trigger auto-scroll (default: 5)
 * @param {boolean} options.enableTouchSupport - Whether to enable touch device support (default: true)
 */
function initAutoScroll(options = {}) {
  // Default options
  const config = {
    targetSelector: ".elementor-widget-ucaddon_image_block_header",
    scrollSpeed: 400, // Faster animation speed
    scrollThreshold: 1,
    enableTouchSupport: true,
    ...options
  };
  
  // Variables to track scroll state
  let isScrolling = false;
  let hasPassedFirstViewport = false;
  
  // Get target position - with fallback if element doesn't exist
  const $targetElement = $(config.targetSelector);
  
  // If target element doesn't exist, exit early
  if (!$targetElement.length) {
    console.log("Target element not found:", config.targetSelector);
    return;
  }
  
  // Get the bottom of the target element instead of the top
  const targetScrollPoint = $targetElement.offset().top + $targetElement.outerHeight();
  
  // Get viewport height
  const viewportHeight = $(window).height();
  
  // Use wheel event for more immediate detection with passive: false
  window.addEventListener("wheel", function(e) {
    // Only proceed if we're in the first viewport and not already scrolling
    if (!hasPassedFirstViewport && $(window).scrollTop() < viewportHeight && !isScrolling) {
      // Detect scroll direction - respond to even the smallest downward scroll
      if (e.deltaY > 0) {
        try {
          // Attempt to prevent default scroll behavior
          e.preventDefault();
        } catch (err) {}
        
        isScrolling = true;
        
        // Smoothly scroll to the target point - use a faster animation
        $("html, body").animate({
          scrollTop: targetScrollPoint
        }, config.scrollSpeed, function() {
          // Animation complete - mark that we've passed first viewport
          hasPassedFirstViewport = true;
          isScrolling = false;
        });
      }
    }
  }, { passive: false });
  
  // Touch support with passive: false
  if (config.enableTouchSupport) {
    let touchStartY = 0;
    
    // Use jQuery's on method for touchstart
    $(window).on("touchstart", function(e) {
      touchStartY = e.originalEvent.touches[0].clientY;
    });
    
    // Add non-passive event listener for touchmove
    window.addEventListener("touchmove", function(e) {
      if (!hasPassedFirstViewport && $(window).scrollTop() < viewportHeight && !isScrolling) {
        const touchY = e.touches[0].clientY;
        const touchDiff = touchStartY - touchY;
        
        // If scrolling down - respond to even smaller movements
        if (touchDiff > config.scrollThreshold) {
          try {
            e.preventDefault();
          } catch (err) {}
          
          isScrolling = true;
          
          $("html, body").animate({
            scrollTop: targetScrollPoint
          }, config.scrollSpeed, function() {
            hasPassedFirstViewport = true;
            isScrolling = false;
          });
        }
      }
    }, { passive: false });
  }
  
  // Backup scroll handler - make this more responsive
  $(window).on("scroll", function() {
    // If we've scrolled even slightly and we're still in first viewport
    if (!hasPassedFirstViewport && 
        $(window).scrollTop() > 0 && // Detect any scroll
        $(window).scrollTop() < viewportHeight && 
        !isScrolling) {
      
      isScrolling = true;
      
      // Smoothly scroll to the target point
      $("html, body").animate({
        scrollTop: targetScrollPoint
      }, config.scrollSpeed, function() {
        hasPassedFirstViewport = true;
        isScrolling = false;
      });
    } else if ($(window).scrollTop() >= viewportHeight) {
      // User has scrolled past first viewport, allow normal scrolling
      hasPassedFirstViewport = true;
    }
  });
  
  // Reset when scrolled back to top
  $(window).on("scrollend scroll", function() {
    if ($(window).scrollTop() === 0) {
      hasPassedFirstViewport = false;
    }
  });
}

// Usage examples:
$(document).ready(function() {
  // Initialize immediately with minimal delay
  setTimeout(function() {
    initAutoScroll({
      targetSelector: ".elementor-widget-ucaddon_image_block_header",
      scrollSpeed: 400,  // Faster animation for less delay
      scrollThreshold: 0.5 // More sensitive to detect scrolling sooner
    });
  }, 100); // Reduced delay to 100ms
});</script>
