-- SQL script to create the saved_nicks table for the Denizen nick save system
-- Run this script on your MySQL database before using the modified Denizen script

CREATE TABLE IF NOT EXISTS saved_nicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_uuid VARCHAR(36) NOT NULL,
    slot_number INT NOT NULL,
    nickname TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_player_slot (player_uuid, slot_number),
    INDEX idx_player_uuid (player_uuid),
    INDEX idx_slot_number (slot_number)
);

-- Optional: Add some sample data for testing
-- INSERT INTO saved_nicks (player_uuid, slot_number, nickname) VALUES 
-- ('550e8400-e29b-41d4-a716-************', 1, '&cRed&fNick'),
-- ('550e8400-e29b-41d4-a716-************', 2, '&9Blue&fPlayer');
