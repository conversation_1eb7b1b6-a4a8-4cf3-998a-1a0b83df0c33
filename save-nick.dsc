saveNickCommand:
    type: command
    name: savenick
    description: Saves a nickname for later/easy use!
    usage: /savenick
    aliases:
    - savednick
    - nicksave
    - savednickname
    - nicknamesave
    tab complete:
    - determine slot|save|gui
    script:
    - if <context.args.get[1]> == "slot" && <player.has_permission[group.nickname]> || <player.has_permission[tlb.nickSave9]> || <player.has_permission[tlb.nickSave18]> || <player.has_permission[tlb.nickSave27]>:
      - if <context.args.get[2].is_decimal>:
        - if <context.args.get[3]||null> != null:
          - if <proc[confirmSlots].context[<context.args.get[2]>|<player>]>:
            #- flag <player> savedNick<context.args.get[2]>:<context.args.get[3]>
            - execute as_server "ee savenick saveother <context.args.get[2]> <player.name> <context.args.get[3]>"
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <context.args.get[3].parse_color> <&b><&l>to slot <context.args.get[2]>"
            - stop
          - else:
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>This slot is invalid/you do not have access to it. To save a nick, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
            - stop
          - else:
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick in a slot, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
      - else: 
        - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick in a slot, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
    - if <context.args.get[1]> == "save" && <player.has_permission[group.nickname]> || <player.has_permission[tlb.nickSave9]> || <player.has_permission[tlb.nickSave18]> || <player.has_permission[tlb.nickSave27]>:
      - if <context.args.get[2].is_decimal>:
        - if <proc[confirmSlots].context[<context.args.get[2]>|<player>]>:
          #- flag <player> savedNick<context.args.get[2]>:<player.display_name>
          - execute as_server "ee savenick saveother <context.args.get[2]> <player.name> <player.display_name>"
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <player.display_name> <&b><&l>to slot <context.args.get[2]>"
          - stop
        - else:
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>This slot is invalid/you do not have access to it. To save a nick, do /savenick save"
          - stop

# /savenick saveother 4 chrismiwggs

    - if <context.args.get[1]||null> == null || <context.args.get[1]||null> == gui:
      - if <player.has_permission[tlb.nickSave27]>:
        - inventory open d:nicksave_27
        - determine FUFILLED
        - stop
      - else if <player.has_permission[tlb.nickSave18]>:
        - inventory open d:nicksave_18
        - determine FUFILLED
        - stop
      - else if <player.has_permission[tlb.nickSave9]>:
        - inventory open d:nicksave_9
        - determine FUFILLED
        - stop
      - else if <player.has_permission[group.nickname]>:
        - inventory open d:nicksave_3
        - determine FUFILLED
        - stop
      - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>You do not have this perk! Unlock it on the <&b>/store <&6>or in <&b>/perks<&6>."

    - else if <context.args.get[1]> == saveother && <player.has_permission[tlb.nickSaveAdmin]> || <context.server>:
      - if <context.args.get[2].is_decimal>:
        #- if <proc[confirmSlots].context[<context.args.get[2]>|<server.match_offline_player[<context.args.get[3]>]>]>:
          - flag <server.match_offline_player[<context.args.get[3]>]> savedNick<context.args.get[2]>:<context.args.get[4]>
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <context.args.get[4].parse_color><&b><&l> <&b><&l>for player <context.args.get[3]><&b><&l>, to slot <context.args.get[2]>"
          - stop
       # - else:
        #  - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>This slot is invalid/they do not have access to it. To save a nick, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
          - stop
      - else:
        - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>Slot must be a number! To save a nick, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
        - stop

    - else:
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick, do /savenick save <&lt>slot<&gt> <&lt>nickname<&gt>"
          - stop

confirmSlots:
  type: procedure
  definitions: slot|player
  script:
  - if <[player].has_permission[tlb.nickSave9]>:
    - if <[slot]> > 0 && <[slot]> <= 9:
      - determine true
  
  - else if <[player].has_permission[tlb.nickSave18]>:
    - if <[slot]> > 0 && <[slot]> <= 18:
      - determine true
  
  - else if <[player].has_permission[tlb.nickSave27]>:
    - if <[slot]> > 0 && <[slot]> <= 27:
      - determine true
      
  - else if <[player].has_permission[group.nickname]>:
    - if <[slot]> > 0 && <[slot]> <= 3:
      - determine true
      
  - else:
    - determine false

returnNick:
  type: procedure
  definitions: slot|player
  script:
  - if <[player].flag[savedNick<[slot]>]||null> == null:
    - determine false
  - else:
    - determine true

nicksave_3:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>3 Slots
  size: 9
  gui: true
  slots:
  - [closedSlot] [closedSlot] [closedSlot] [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [closedSlot] [closedSlot] [closedSlot]

nicksave_9:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>9 Slots
  size: 9
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]

nicksave_18:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>18 Slots
  size: 18
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[10|<player>]>].pass[nickSlot10].fail[emptySlot]>] [<tern[<proc[returnNick].context[11|<player>]>].pass[nickSlot11].fail[emptySlot]>] [<tern[<proc[returnNick].context[12|<player>]>].pass[nickSlot12].fail[emptySlot]>] [<tern[<proc[returnNick].context[13|<player>]>].pass[nickSlot13].fail[emptySlot]>] [<tern[<proc[returnNick].context[14|<player>]>].pass[nickSlot14].fail[emptySlot]>] [<tern[<proc[returnNick].context[15|<player>]>].pass[nickSlot15].fail[emptySlot]>] [<tern[<proc[returnNick].context[16|<player>]>].pass[nickSlot16].fail[emptySlot]>] [<tern[<proc[returnNick].context[17|<player>]>].pass[nickSlot17].fail[emptySlot]>] [<tern[<proc[returnNick].context[18|<player>]>].pass[nickSlot18].fail[emptySlot]>]

nicksave_27:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>27 Slots
  size: 27
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[10|<player>]>].pass[nickSlot10].fail[emptySlot]>] [<tern[<proc[returnNick].context[11|<player>]>].pass[nickSlot11].fail[emptySlot]>] [<tern[<proc[returnNick].context[12|<player>]>].pass[nickSlot12].fail[emptySlot]>] [<tern[<proc[returnNick].context[13|<player>]>].pass[nickSlot13].fail[emptySlot]>] [<tern[<proc[returnNick].context[14|<player>]>].pass[nickSlot14].fail[emptySlot]>] [<tern[<proc[returnNick].context[15|<player>]>].pass[nickSlot15].fail[emptySlot]>] [<tern[<proc[returnNick].context[16|<player>]>].pass[nickSlot16].fail[emptySlot]>] [<tern[<proc[returnNick].context[17|<player>]>].pass[nickSlot17].fail[emptySlot]>] [<tern[<proc[returnNick].context[18|<player>]>].pass[nickSlot18].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[19|<player>]>].pass[nickSlot19].fail[emptySlot]>] [<tern[<proc[returnNick].context[20|<player>]>].pass[nickSlot20].fail[emptySlot]>] [<tern[<proc[returnNick].context[21|<player>]>].pass[nickSlot21].fail[emptySlot]>] [<tern[<proc[returnNick].context[22|<player>]>].pass[nickSlot22].fail[emptySlot]>] [<tern[<proc[returnNick].context[23|<player>]>].pass[nickSlot23].fail[emptySlot]>] [<tern[<proc[returnNick].context[24|<player>]>].pass[nickSlot24].fail[emptySlot]>] [<tern[<proc[returnNick].context[25|<player>]>].pass[nickSlot25].fail[emptySlot]>] [<tern[<proc[returnNick].context[26|<player>]>].pass[nickSlot26].fail[emptySlot]>] [<tern[<proc[returnNick].context[27|<player>]>].pass[nickSlot27].fail[emptySlot]>]
closedSlot:
  type: item
  display name: <&4><&l>LOCKED
  material: BARRIER
  lore:
  - "<&6>Purchase extra saved nicknames on the <&b><&l>/store <&6>or <&b><&l>/perks"
    
    
emptySlot:
  type: item
  display name: <&6><&l>EMPTY SLOT
  material: GLASS_PANE
  lore:
  - "<&b>Use the command <&b><&l>/savenick save <&lt>slot<&gt> <&6>to save a nickname here!"
    
    
nickSlot1:
  type: item
  display name: "<&7><&l>Nick Slot 1"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick1].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot2:
  type: item
  display name: "<&7><&l>Nick Slot 2"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick2].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot3:
  type: item
  display name: "<&7><&l>Nick Slot 3"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick3].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot4:
  type: item
  display name: "<&7><&l>Nick Slot 4"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick4].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot5:
  type: item
  display name: "<&7><&l>Nick Slot 5"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick5].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot6:
  type: item
  display name: "<&7><&l>Nick Slot 6"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick6].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot7:
  type: item
  display name: "<&7><&l>Nick Slot 7"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick7].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot8:
  type: item
  display name: "<&7><&l>Nick Slot 8"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick8].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot9:
  type: item
  display name: "<&7><&l>Nick Slot 9"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick9].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot10:
  type: item
  display name: "<&7><&l>Nick Slot 10"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick10].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot11:
  type: item
  display name: "<&7><&l>Nick Slot 11"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick11].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot12:
  type: item
  display name: "<&7><&l>Nick Slot 12"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick12].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot13:
  type: item
  display name: "<&7><&l>Nick Slot 13"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick13].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot14:
  type: item
  display name: "<&7><&l>Nick Slot 14"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick14].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot15:
  type: item
  display name: "<&7><&l>Nick Slot 15"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick15].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot16:
  type: item
  display name: "<&7><&l>Nick Slot 16"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick16].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot17:
  type: item
  display name: "<&7><&l>Nick Slot 17"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick17].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot18:
  type: item
  display name: "<&7><&l>Nick Slot 18"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick18].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot19:
  type: item
  display name: "<&7><&l>Nick Slot 19"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick19].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot20:
  type: item
  display name: "<&7><&l>Nick Slot 20"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick20].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot21:
  type: item
  display name: "<&7><&l>Nick Slot 21"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick21].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot22:
  type: item
  display name: "<&7><&l>Nick Slot 22"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick22].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot23:
  type: item
  display name: "<&7><&l>Nick Slot 23"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick23].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot24:
  type: item
  display name: "<&7><&l>Nick Slot 24"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick24].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot25:
  type: item
  display name: "<&7><&l>Nick Slot 25"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick25].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot26:
  type: item
  display name: "<&7><&l>Nick Slot 26"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick26].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot27:
  type: item
  display name: "<&7><&l>Nick Slot 27"
  material: NETHER_STAR
  lore:
  - "<player.flag[savedNick27].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
    
nickSaveListerners:
  type: world
  events:
    on player clicks nickSlot1 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick1].to_minimessage>"
    - inventory close

    on player clicks nickSlot2 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick2].to_minimessage>"
    - inventory close

    on player clicks nickSlot3 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick3].to_minimessage>"
    - inventory close

    on player clicks nickSlot4 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick4].to_minimessage>"
    - inventory close

    on player clicks nickSlot5 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick5].to_minimessage>"
    - inventory close

    on player clicks nickSlot6 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick6].to_minimessage>"
    - inventory close

    on player clicks nickSlot7 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick7].to_minimessage>"
    - inventory close

    on player clicks nickSlot8 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick8].to_minimessage>"
    - inventory close

    on player clicks nickSlot9 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick9].to_minimessage>"
    - inventory close

    on player clicks nickSlot10 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick10].to_minimessage>"
    - inventory close

    on player clicks nickSlot11 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick11].to_minimessage>"
    - inventory close

    on player clicks nickSlot12 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick12].to_minimessage>"
    - inventory close

    on player clicks nickSlot13 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick13].to_minimessage>"
    - inventory close

    on player clicks nickSlot14 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick14].to_minimessage>"
    - inventory close

    on player clicks nickSlot15 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick15].to_minimessage>"
    - inventory close

    on player clicks nickSlot16 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick16].to_minimessage>"
    - inventory close

    on player clicks nickSlot17 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick17].to_minimessage>"
    - inventory close

    on player clicks nickSlot18 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick18].to_minimessage>"
    - inventory close

    on player clicks nickSlot19 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick19].to_minimessage>"
    - inventory close

    on player clicks nickSlot20 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick20].to_minimessage>"
    - inventory close

    on player clicks nickSlot21 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick21].to_minimessage>"
    - inventory close

    on player clicks nickSlot22 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick22].to_minimessage>"
    - inventory close

    on player clicks nickSlot23 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick23].to_minimessage>"
    - inventory close

    on player clicks nickSlot24 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick24].to_minimessage>"
    - inventory close

    on player clicks nickSlot25 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick25].to_minimessage>"
    - inventory close

    on player clicks nickSlot26 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick26].to_minimessage>"
    - inventory close

    on player clicks nickSlot27 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick27].to_minimessage>"
    - inventory close
