# MySQL Setup Instructions for Denizen Nick Save System

## Overview
This modified Denizen script now uses MySQL database instead of Denizen's flag storage system for saving and loading player nicknames. This provides better persistence and performance for larger servers.

## Prerequisites
- MySQL server running and accessible
- Denizen plugin with SQL support
- Appropriate MySQL user with database permissions

## Setup Steps

### 1. Database Setup
1. Create a MySQL database for your Minecraft server (if you don't have one already)
2. Run the `create_saved_nicks_table.sql` script to create the required table:
   ```sql
   mysql -u your_username -p your_database < create_saved_nicks_table.sql
   ```

### 2. Configure Database Connection
Edit the `mysqlConfig` section at the top of the `save-nick.dsc` file:

```yaml
mysqlConfig:
  type: data
  host: localhost          # Your MySQL server host
  port: 3306              # Your MySQL server port
  database: minecraft     # Your database name
  username: minecraft_user # Your MySQL username
  password: your_password_here # Your MySQL password
  table: saved_nicks      # Table name (don't change unless you modify the SQL)
```

### 3. Database Permissions
Ensure your MySQL user has the following permissions on the database:
- SELECT
- INSERT
- UPDATE
- DELETE (optional, for future cleanup features)

Example SQL to grant permissions:
```sql
GRANT SELECT, INSERT, UPDATE ON minecraft.saved_nicks TO 'minecraft_user'@'localhost';
FLUSH PRIVILEGES;
```

## How It Works

### Data Flow
1. **Player Join**: When a player joins, their saved nicknames are loaded from MySQL into temporary flags
2. **Save Nickname**: When a nickname is saved, it's immediately written to MySQL database
3. **Player Leave**: When a player leaves, all their current nicknames are saved to MySQL database
4. **GUI Display**: The inventory GUI reads from the temporary flags (loaded from MySQL)

### Database Schema
The `saved_nicks` table structure:
- `id`: Auto-increment primary key
- `player_uuid`: Player's UUID (VARCHAR 36)
- `slot_number`: Slot number (1-27) (INT)
- `nickname`: The saved nickname with color codes (TEXT)
- `created_at`: When the record was created (TIMESTAMP)
- `updated_at`: When the record was last updated (TIMESTAMP)

### Key Features
- **Immediate Save**: Nicknames are saved to database immediately when created
- **Automatic Load**: Player nicknames are loaded automatically on join
- **Backup on Leave**: All nicknames are backed up to database when player leaves
- **Duplicate Prevention**: Uses `ON DUPLICATE KEY UPDATE` to handle slot overwrites
- **SQL Injection Protection**: Uses parameterized queries and escapes single quotes

## Migration from Flag System
If you have existing saved nicknames in Denizen flags, you'll need to create a migration script or manually transfer the data. The old flag format was `savedNick1`, `savedNick2`, etc.

## Troubleshooting

### Common Issues
1. **Connection Failed**: Check database credentials and network connectivity
2. **Table Not Found**: Ensure you've run the SQL creation script
3. **Permission Denied**: Verify MySQL user permissions
4. **Encoding Issues**: Ensure your MySQL database uses UTF-8 encoding for proper color code support

### Debug Tips
- Check Denizen console for SQL error messages
- Test database connection manually with MySQL client
- Verify table structure matches the expected schema
- Check that the MySQL user has proper permissions

## Performance Notes
- The system loads all nicknames for a player on join (typically 1-27 records max)
- Saves are individual INSERT/UPDATE operations per nickname
- Consider adding database indexes if you have a very large player base
- The current implementation prioritizes data integrity over performance

## Security Considerations
- Use a dedicated MySQL user with minimal required permissions
- Consider using SSL connections for remote MySQL servers
- Regularly backup your database
- Monitor for unusual database activity
